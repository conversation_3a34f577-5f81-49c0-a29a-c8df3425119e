// pages/product/detail.js
const { productApi, cartApi } = require('../../utils/api');

Page({
  data: {
    product: null,
    loading: true,
    currentSwiperIndex: 0,
    cartCount: 0
  },

  onLoad: function(options) {
    console.log('商品详情页 onLoad:', {
      options: options,
      id: options.id,
      idType: typeof options.id
    });
    
    const { id } = options;
    if (id) {
      this.getProductDetail(id);
    } else {
      wx.showToast({
        title: '商品ID不能为空',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  onShow: function() {
    // 更新购物车数量
    this.getCartCount();
  },

  // 获取商品详情
  getProductDetail: function(id) {
    console.log('准备获取商品详情, id:', id, 'id类型:', typeof id);
    
    this.setData({
      loading: true
    });
    
    productApi.getProductById(id)
      .then(res => {
        console.log('获取商品详情响应:', res);
        if (res.success) {
          let product = res.data;
          
          // 处理商品图片
          if (product.images && typeof product.images === 'string') {
            try {
              product.images = JSON.parse(product.images);
            } catch (e) {
              console.error('解析商品图片失败:', e);
              product.images = [];
            }
          }
          
          // 确保 _id 字段存在
          if (!product._id && product.id) {
            product._id = product.id;
          }
          
          this.setData({
            product: product,
            loading: false
          });
        } else {
          wx.showToast({
            title: res.message || '获取商品详情失败',
            icon: 'none'
          });
          
          this.setData({
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('获取商品详情异常:', err);
        
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
        
        this.setData({
          loading: false
        });
      });
  },
  
  // 轮播图切换
  onSwiperChange: function(e) {
    this.setData({
      currentSwiperIndex: e.detail.current
    });
  },
  
  // 加入购物车
  addToCart: function() {
    const app = getApp();
    const id = this.data.product._id;
    
    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performAddToCart(id);
      }
    })) {
      return;
    }
    
    this.performAddToCart(id);
  },
  
  // 执行添加到购物车操作
  performAddToCart: function(id) {
    // 显示加载中
    wx.showLoading({
      title: '正在添加到购物车',
      mask: true
    });
    
    // 使用API添加商品到购物车
    cartApi.addToCart(id, 1)
      .then(res => {
        wx.hideLoading();
        
        if (res.success) {
          // 显示成功提示
          wx.showToast({
            title: '已加入购物车',
            icon: 'success'
          });
          
          // 更新购物车数量
          this.getCartCount();
        } else {
          wx.showToast({
            title: res.message || '添加失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('添加到购物车失败', err);
        wx.hideLoading();
        
        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  },
  
  // 立即购买
  buyNow: function() {
    const app = getApp();
    const id = this.data.product._id;
    
    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performBuyNow(id);
      }
    })) {
      return;
    }
    
    this.performBuyNow(id);
  },
  
  // 执行立即购买操作
  performBuyNow: function(id) {
    // 显示加载中
    wx.showLoading({
      title: '正在处理',
      mask: true
    });
    
    // 使用API添加商品到购物车
    cartApi.addToCart(id, 1)
      .then(res => {
        wx.hideLoading();
        
        if (res.success) {
          // 跳转到结算页面
          wx.navigateTo({
            url: '/pages/order/checkout?from=buy_now&id=' + id
          });
        } else {
          wx.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('立即购买失败', err);
        wx.hideLoading();
        
        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  },
  
  // 获取购物车数量
  getCartCount: function() {
    // 从本地存储获取购物车数据
    const cartItems = wx.getStorageSync('cartItems') || [];
    
    // 计算购物车中商品总数量
    let count = 0;
    cartItems.forEach(item => {
      count += (item.quantity || 1);
    });
    
    // 更新数据
    this.setData({
      cartCount: count
    });
  },
  
  // 跳转到购物车页面
  goToCart: function() {
    wx.switchTab({
      url: '/pages/cart/cart'
    });
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },
  
  // 分享
  onShareAppMessage: function() {
    const product = this.data.product;
    
    return {
      title: product ? product.name : '企业服务商品',
      path: '/pages/product/detail?id=' + (product ? product._id : ''),
      imageUrl: product && product.images && product.images.length > 0 ? product.images[0] : ''
    };
  }
});
