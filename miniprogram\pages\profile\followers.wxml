<view class="followers-page">
  <view wx:if="{{loading}}" class="loading">加载中...</view>
  <view wx:elif="{{!followers.length}}" class="empty">暂无粉丝</view>
  <block wx:else>
    <view class="followers-list">
      <block wx:for="{{followers}}" wx:key="id">
        <view class="follower-item">
          <view class="user-info-area" bindtap="onUserTap" data-id="{{item.id}}">
            <image class="avatar" src="{{item.avatar || '/images/icons2/男头像.png'}}"></image>
            <view class="info">
              <view class="nickname">{{item.nickName || item.nickname}}</view>
              <view class="userid">ID: {{item.id}}</view>
            </view>
          </view>
          <view class="follow-btn {{item.isFollowed ? 'followed' : ''}}"
                bindtap="onFollowTap"
                data-id="{{item.id}}"
                data-index="{{index}}">
            {{item.isFollowed ? '已关注' : '关注'}}
          </view>
        </view>
      </block>
    </view>
  </block>
</view>