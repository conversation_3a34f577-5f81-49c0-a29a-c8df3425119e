const { userApi } = require('../../utils/api');
const app = getApp();

Page({
  data: {
    followers: [],
    loading: true
  },
  onLoad: function() {
    this.getFollowers();
  },
  onShow() {
    // 每次页面显示时同步本地 token 到全局
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    if (token && userInfo) {
      app.globalData.isLogin = true;
      app.globalData.userInfo = userInfo;
    } else {
      app.globalData.isLogin = false;
      app.globalData.userInfo = null;
    }
    // 检查登录状态
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '未登录',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateTo({ url: '/pages/auth/auth' });
        }
      });
      return;
    }
    // 已登录则刷新粉丝列表
    this.getFollowers();
  },
  getFollowers: function() {
    this.setData({ loading: true });
    userApi.getMyFansList().then(res => {
      if (res.success && Array.isArray(res.data)) {
        const followers = res.data;
        this.setData({ followers, loading: false });
        // 获取关注状态
        this.checkFollowStatus(followers);
      } else {
        this.setData({ followers: [], loading: false });
      }
    }).catch(() => {
      this.setData({ followers: [], loading: false });
    });
  },

  // 检查关注状态
  checkFollowStatus: function(followers) {
    if (!app.globalData.isLogin || !followers.length) return;

    const promises = followers.map((follower, index) => {
      return userApi.checkFollowStatus(follower.id).then(res => {
        if (res.success) {
          followers[index].isFollowed = res.data.isFollowing;
        } else {
          followers[index].isFollowed = false;
        }
      }).catch(() => {
        followers[index].isFollowed = false;
      });
    });

    Promise.all(promises).then(() => {
      this.setData({ followers });
    });
  },

  onUserTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/user/space?id=${id}`
    });
  },

  // 关注/取消关注
  onFollowTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;
    const followers = this.data.followers;
    const follower = followers[index];

    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: true,
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.redirectTo({ url: '/pages/auth/auth?redirect=/pages/profile/followers' });
          }
        }
      });
      return;
    }

    // 不能关注自己
    if (id === app.globalData.userInfo.id || id === app.globalData.userInfo.userId) {
      wx.showToast({ title: '不能关注自己', icon: 'none' });
      return;
    }

    const isFollowed = follower.isFollowed;
    const action = isFollowed ? '取消关注' : '关注';

    wx.showLoading({ title: `${action}中...`, mask: true });

    // 立即更新UI状态
    followers[index].isFollowed = !isFollowed;
    this.setData({ followers });

    const api = isFollowed ? userApi.unfollowUser : userApi.followUser;

    api(id).then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.showToast({
          title: isFollowed ? '已取消关注' : '已关注',
          icon: 'success'
        });
      } else {
        // 操作失败，恢复之前的状态
        followers[index].isFollowed = isFollowed;
        this.setData({ followers });
        wx.showToast({
          title: res.message || `${action}失败`,
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      // 请求失败，恢复之前的状态
      followers[index].isFollowed = isFollowed;
      this.setData({ followers });
      console.error(`${action}失败:`, err);
      wx.showToast({
        title: '网络错误，请稍后再试',
        icon: 'none'
      });
    });
  }
});