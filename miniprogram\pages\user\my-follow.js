const app = getApp();
const { userApi } = require('../../utils/api');

Page({
  data: {
    followList: [],
    loading: true
  },
  onLoad() {
    this.loadFollowList();
  },
  onShow() {
    // 每次页面显示时同步本地 token 到全局
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    if (token && userInfo) {
      app.globalData.isLogin = true;
      app.globalData.userInfo = userInfo;
    } else {
      app.globalData.isLogin = false;
      app.globalData.userInfo = null;
    }
    // 检查登录状态
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '未登录',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateTo({ url: '/pages/auth/auth' });
        }
      });
      return;
    }
    // 已登录则刷新关注列表
    this.loadFollowList();
  },
  loadFollowList() {
    this.setData({ loading: true });
    userApi.getMyFollowList().then(res => {
      // 我的关注接口返回
      if (res.success && Array.isArray(res.data) && res.data.length > 0) {
        this.setData({ followList: res.data, loading: false });
      } else {
        wx.showToast({ title: res.message || '暂无关注', icon: 'none' });
        this.setData({ followList: [], loading: false });
      }
    }).catch(() => {
      wx.showToast({ title: '网络错误', icon: 'none' });
      this.setData({ loading: false });
    });
  },
  onUserTap(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({ url: `/pages/user/space?id=${id}` });
  },

  // 取消关注
  onUnfollowTap(e) {
    const id = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;
    const followList = this.data.followList;
    const user = followList[index];

    wx.showModal({
      title: '确认取消关注',
      content: `确定要取消关注 ${user.nickName || user.nickname} 吗？`,
      success: (res) => {
        if (res.confirm) {
          this.unfollowUser(id, index);
        }
      }
    });
  },

  // 执行取消关注操作
  unfollowUser(userId, index) {
    wx.showLoading({ title: '取消关注中...', mask: true });

    userApi.unfollowUser(userId).then(res => {
      wx.hideLoading();
      if (res.success) {
        // 从列表中移除该用户
        const followList = this.data.followList;
        followList.splice(index, 1);
        this.setData({ followList });

        wx.showToast({
          title: '已取消关注',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.message || '取消关注失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('取消关注失败:', err);
      wx.showToast({
        title: '网络错误，请稍后再试',
        icon: 'none'
      });
    });
  }
});