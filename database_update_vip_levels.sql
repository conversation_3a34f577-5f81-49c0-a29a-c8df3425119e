-- =====================================================
-- 会员级别数据更新SQL脚本
-- 将现有的4个会员级别修改为5个新级别
-- V1（青铜会员）、V2（白银会员）、V3（黄金会员）、V4（钻石会员）、V5（王者会员）
-- =====================================================

-- 1. 备份现有数据（可选，建议在生产环境执行前先备份）
-- CREATE TABLE vip_levels_backup AS SELECT * FROM vip_levels;
-- CREATE TABLE vip_benefits_backup AS SELECT * FROM vip_benefits;
-- CREATE TABLE user_vip_backup AS SELECT * FROM user_vip;

-- 2. 清空现有会员权益数据
DELETE FROM vip_benefits WHERE level_code IN ('annual', 'super', 'v3');

-- 3. 更新现有会员等级数据
-- 删除旧的会员等级（保留free和v3，v3将被重新定义）
DELETE FROM vip_levels WHERE level_code IN ('annual', 'super');

-- 更新现有的v3为新的v3（黄金会员）
UPDATE vip_levels 
SET level_name = 'V3黄金会员', 
    description = '高级会员，享受更多权益和服务', 
    price = 599 
WHERE level_code = 'v3';

-- 4. 插入新的会员等级
INSERT INTO vip_levels (level_code, level_name, description, price, duration) VALUES
('v1', 'V1青铜会员', '入门级会员，享受基础增值服务', 99, 365),
('v2', 'V2白银会员', '标准会员，享受平台大部分功能和服务', 299, 365),
('v4', 'V4钻石会员', '尊贵会员，享受全部平台权益和个性化服务', 999, 365),
('v5', 'V5王者会员', '至尊会员，享受最高级别的专属服务', 1999, 365);

-- 5. 插入新的会员权益数据
INSERT INTO vip_benefits (level_code, benefit_name, benefit_desc, sort_order) VALUES
-- V1青铜会员权益
('v1', '专属会员标识', '在个人主页和评论中展示青铜会员标识', 1),
('v1', '基础客服支持', '工作日客服优先响应', 2),
('v1', '会员专属活动', '参加平台定期举办的会员活动', 3),
('v1', '基础文档模板', '使用会员专属的基础文档模板', 4),
('v1', '每月免费咨询1次', '每月可获得1次免费专业咨询服务', 5),

-- V2白银会员权益
('v2', '全部青铜会员权益', '包含V1青铜会员的所有权益', 1),
('v2', '白银会员专属标识', '展示更高级的白银会员身份标识', 2),
('v2', '7×12小时专属客服', '优先获得客服响应和解决问题', 3),
('v2', '9折服务优惠', '平台所有付费服务可享受9折优惠', 4),
('v2', '每月免费咨询2次', '每月可获得2次免费专业咨询服务', 5),
('v2', '高级文档模板库', '使用更丰富的文档模板库', 6),

-- V3黄金会员权益
('v3', '全部白银会员权益', '包含V2白银会员的所有权益', 1),
('v3', '黄金会员专属标识', '展示尊贵的黄金会员身份标识', 2),
('v3', '专享85折服务优惠', '平台所有付费服务可享受85折优惠', 3),
('v3', '高级会员专属礼包', '每季度获得一次会员专属礼包', 4),
('v3', '优先参与线下活动', '优先参与平台组织的线下活动', 5),
('v3', '每月免费咨询3次', '每月可获得3次免费专业咨询服务', 6),
('v3', '专属高级模板库', '使用更高级、更全面的文档模板库', 7),

-- V4钻石会员权益
('v4', '全部黄金会员权益', '包含V3黄金会员的所有权益', 1),
('v4', '钻石会员专属标识', '展示顶级的钻石会员身份标识', 2),
('v4', '一对一专属服务顾问', '指定专属顾问提供一对一服务', 3),
('v4', '专享8折服务优惠', '平台所有付费服务可享受8折优惠', 4),
('v4', '钻石专属高级定制服务', '根据个人需求提供定制化服务', 5),
('v4', '每月免费咨询5次', '每月可获得5次免费专业咨询服务', 6),
('v4', '线下活动免费参与', '免费参与大部分线下活动', 7),
('v4', '项目优先对接', '业务需求优先对接专业服务团队', 8),

-- V5王者会员权益
('v5', '全部钻石会员权益', '包含V4钻石会员的所有权益', 1),
('v5', '王者会员至尊标识', '展示最高级别的王者会员身份标识', 2),
('v5', '专属VIP服务团队', '配备专属的VIP服务团队', 3),
('v5', '专享7折服务优惠', '平台所有付费服务可享受7折优惠', 4),
('v5', '王者专属定制服务', '享受最高级别的个性化定制服务', 5),
('v5', '无限制专业咨询', '不限次数的专业咨询服务', 6),
('v5', '所有线下活动免费', '免费参与所有线下活动和专属聚会', 7),
('v5', '优先项目资源对接', '最优先的项目和资源对接服务', 8),
('v5', '年度专属礼品', '每年获得价值不菲的专属定制礼品', 9);

-- 6. 更新现有用户的会员等级映射
-- 将原来的annual会员用户更新为v2
UPDATE user_vip SET level_code = 'v2' WHERE level_code = 'annual';

-- 将原来的super会员用户更新为v3  
UPDATE user_vip SET level_code = 'v3' WHERE level_code = 'super';

-- v3会员保持不变（已经是v3）

-- 7. 验证数据更新结果
-- SELECT * FROM vip_levels ORDER BY price ASC;
-- SELECT level_code, COUNT(*) as benefit_count FROM vip_benefits GROUP BY level_code ORDER BY level_code;
-- SELECT level_code, COUNT(*) as user_count FROM user_vip GROUP BY level_code ORDER BY level_code;

-- =====================================================
-- 执行完成后，数据库将包含以下会员级别：
-- free (普通用户) - ¥0
-- v1 (V1青铜会员) - ¥99
-- v2 (V2白银会员) - ¥299  
-- v3 (V3黄金会员) - ¥599
-- v4 (V4钻石会员) - ¥999
-- v5 (V5王者会员) - ¥1999
-- =====================================================
