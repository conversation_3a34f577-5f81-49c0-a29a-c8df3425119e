/**
 * 云托管环境 - Comments表字段迁移脚本
 * 用于添加 userNickname 和 userAvatar 字段
 */

const db = require('./config/db');

async function migrateCommentsTable() {
  try {
    console.log('=== 开始Comments表迁移 ===');
    
    // 1. 检查comments表是否存在
    console.log('1. 检查comments表...');
    const tables = await db.query('SHOW TABLES LIKE "comments"');
    if (tables.length === 0) {
      console.error('❌ comments表不存在！');
      return;
    }
    console.log('✅ comments表存在');
    
    // 2. 检查现有字段
    console.log('2. 检查现有字段...');
    const columns = await db.query('SHOW COLUMNS FROM comments');
    const columnNames = columns.map(col => col.Field);
    console.log('现有字段:', columnNames);
    
    // 3. 添加userNickname字段（如果不存在）
    if (!columnNames.includes('userNickname')) {
      console.log('3. 添加userNickname字段...');
      await db.query('ALTER TABLE comments ADD COLUMN userNickname VARCHAR(100)');
      console.log('✅ userNickname字段添加成功');
    } else {
      console.log('✅ userNickname字段已存在');
    }
    
    // 4. 添加userAvatar字段（如果不存在）
    if (!columnNames.includes('userAvatar')) {
      console.log('4. 添加userAvatar字段...');
      await db.query('ALTER TABLE comments ADD COLUMN userAvatar VARCHAR(255)');
      console.log('✅ userAvatar字段添加成功');
    } else {
      console.log('✅ userAvatar字段已存在');
    }
    
    // 5. 验证迁移结果
    console.log('5. 验证迁移结果...');
    const updatedColumns = await db.query('SHOW COLUMNS FROM comments');
    const updatedColumnNames = updatedColumns.map(col => col.Field);
    console.log('更新后的字段:', updatedColumnNames);
    
    // 6. 检查数据
    console.log('6. 检查现有数据...');
    const commentCount = await db.query('SELECT COUNT(*) as count FROM comments');
    console.log(`comments表中有 ${commentCount[0].count} 条记录`);
    
    // 7. 测试查询
    console.log('7. 测试getMyComments查询...');
    const testQuery = `
      SELECT c.id as commentId, c.content as myComment, c.createTime as commentTime,
             p.id as postId, p.content as postContent, p.userId as postUserId, p.createTime as postCreateTime,
             COALESCE(u.nickname, u.nickName, '用户') as postUserNickname,
             COALESCE(u.avatar, '/images/icons2/男头像.png') as postUserAvatar
      FROM comments c
      INNER JOIN posts p ON c.postId = p.id
      INNER JOIN users u ON p.userId = u.id
      LIMIT 1
    `;
    
    try {
      const testResult = await db.query(testQuery);
      console.log('✅ 查询测试成功，返回', testResult.length, '条记录');
      if (testResult.length > 0) {
        console.log('示例数据:', {
          commentId: testResult[0].commentId,
          postUserNickname: testResult[0].postUserNickname,
          postUserAvatar: testResult[0].postUserAvatar
        });
      }
    } catch (queryError) {
      console.error('❌ 查询测试失败:', queryError.message);
    }
    
    console.log('=== Comments表迁移完成 ===');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    throw error;
  }
}

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  migrateCommentsTable()
    .then(() => {
      console.log('✅ 迁移成功完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 迁移失败:', error);
      process.exit(1);
    });
}

module.exports = { migrateCommentsTable };
