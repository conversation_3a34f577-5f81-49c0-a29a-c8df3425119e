/**
 * 会员控制器
 */
const VipMember = require('../models/VipMember');

/**
 * 获取会员等级列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getVipLevels = async (req, res) => {
  try {
    const levels = await VipMember.getVipLevels();
    res.json({ success: true, data: levels });
  } catch (error) {
    console.error('获取会员等级列表失败:', error);
    res.status(500).json({ success: false, message: '获取会员等级列表失败' });
  }
};

/**
 * 获取会员权益
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getVipBenefits = async (req, res) => {
  try {
    const { levelCode } = req.query;

    if (levelCode) {
      // 获取指定等级的权益
      const benefits = await VipMember.getVipBenefits(levelCode);
      res.json({ success: true, data: benefits });
    } else {
      // 获取所有等级的权益
      const allBenefits = await VipMember.getAllVipBenefits();
      res.json({ success: true, data: allBenefits });
    }
  } catch (error) {
    console.error('获取会员权益失败:', error);
    res.status(500).json({ success: false, message: '获取会员权益失败' });
  }
};

/**
 * 获取用户会员信息
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getUserVipInfo = async (req, res) => {
  try {
    const userId = req.userId || req.query.userId;

    if (!userId) {
      return res.status(400).json({ success: false, message: '缺少用户ID' });
    }

    const vipInfo = await VipMember.getUserVipInfo(userId);
    const formattedInfo = VipMember.formatVipInfoForDisplay(vipInfo);

    res.json({ success: true, data: formattedInfo });
  } catch (error) {
    console.error('获取用户会员信息失败:', error);
    res.status(500).json({ success: false, message: '获取用户会员信息失败' });
  }
};

/**
 * 获取会员产品列表
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getVipProducts = async (req, res) => {
  try {
    const products = await VipMember.getVipProducts();
    res.json({ success: true, data: products });
  } catch (error) {
    console.error('获取会员产品列表失败:', error);
    res.status(500).json({ success: false, message: '获取会员产品列表失败' });
  }
};

/**
 * 更新用户会员信息
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateUserVip = async (req, res) => {
  try {
    const userId = req.userId;
    const { levelCode, duration, paymentId } = req.body;

    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    if (!levelCode) {
      return res.status(400).json({ success: false, message: '缺少会员等级编码' });
    }

    // 获取会员等级信息
    const levelInfo = await VipMember.getVipLevelByCode(levelCode);
    if (!levelInfo) {
      return res.status(400).json({ success: false, message: '无效的会员等级' });
    }

    // 设置过期时间
    const expireTime = new Date();
    expireTime.setDate(expireTime.getDate() + (duration || levelInfo.duration));

    // 更新用户会员信息
    const updatedVipInfo = await VipMember.updateUserVip(userId, levelCode, expireTime, paymentId);
    const formattedInfo = VipMember.formatVipInfoForDisplay(updatedVipInfo);

    res.json({ success: true, data: formattedInfo });
  } catch (error) {
    console.error('更新用户会员信息失败:', error);
    res.status(500).json({ success: false, message: '更新用户会员信息失败' });
  }
};
