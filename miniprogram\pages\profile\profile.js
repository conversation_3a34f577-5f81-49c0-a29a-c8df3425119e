// pages/profile/profile.js
const { userApi, vipApi, pointsApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');
const request = require('../../utils/request');

Page({  data: {
    userInfo: null,
    stats: {
      followCount: 125,
      followerCount: 23,
      likeCount: 88,
      favoriteCount: 0,
      points: 100,
      balance: 0,
      unpaidCount: 2,
      unshippedCount: 1,
      unreceivedCount: 0,
      unratedCount: 0
    },
    vipInfo: {
      level_code: 'free',
      level_name: '普通用户',
      expire_time: null
    },
    defaultStats: {
      followCount: 0,
      followerCount: 0,
      likeCount: 0,
      favoriteCount: 0,
      points: 0,
      balance: 0,
      unpaidCount: 0,
      unshippedCount: 0,
      unreceivedCount: 0,
      unratedCount: 0,
      postCount: 0,
      commentCount: 0,
      promotionCount: 0
    },
    isLogin: false, // 默认为未登录状态
    loading: false,
    banners: [
      {
        id: 1,
        imageUrl: '/images/lunbo/001.jpeg',
        type: 'page',
        linkUrl: '/pages/shop/shop?category=enterprise',
        title: '企业所得税汇算清缴专题'
      },
      {
        id: 2,
        imageUrl: '/images/lunbo/002.jpg',
        type: 'page',
        linkUrl: '/pages/shop/shop?category=platform',
        title: '知识产权保护与商标注册'
      },
      {
        id: 3,
        imageUrl: '/images/lunbo/003.png',
        type: 'page',
        linkUrl: '/pages/shop/shop?category=partner',
        title: '企业并购与资产重组'
      },
      {
        id: 4,
        imageUrl: '/images/lunbo/004.jpg',
        type: 'page',
        linkUrl: '/pages/shop/shop?category=peripheral',
        title: '公司注册与工商变更'
      }
    ]
  },

  onLoad: function (options) {
    this.checkLoginStatus();
  },
  onShow: function () {
    // 检查登录状态是否发生变化
    const app = getApp();
    const globalData = app.globalData || {};
    console.log('个人中心页面显示，当前登录状态:', globalData.isLogin, '页面状态:', this.data.isLogin);
    console.log('needRefreshProfile标志:', globalData.needRefreshProfile);

    // 每次显示页面时都检查登录状态，确保页面状态与全局状态一致
    this.checkLoginStatus();

    // 如果全局状态显示已登录但页面状态为未登录，强制刷新
    if (globalData.isLogin && !this.data.isLogin) {
      console.log('检测到状态不一致，强制刷新登录状态');
      this.setData({
        isLogin: true,
        userInfo: globalData.userInfo
      });

      // 获取用户统计数据
      this.getUserStats();
      
      // 获取用户VIP信息
      this.getUserVipInfo();
    }
    
    // 如果已登录，刷新VIP信息
    if (this.data.isLogin) {
      this.getUserVipInfo();
    }

    // 重置标记
    globalData.needRefreshProfile = false;
  },

  // 获取轮播图数据
  getBanners: function() {
    // 使用本地数据
    this.setData({
      banners: [
        {
          id: 1,
          imageUrl: '/images/lunbo/001.jpeg',
          type: 'page',
          linkUrl: '/pages/shop/shop?category=enterprise',
          title: '企业所得税汇算清缴专题'
        },
        {
          id: 2,
          imageUrl: '/images/lunbo/002.jpg',
          type: 'page',
          linkUrl: '/pages/shop/shop?category=platform',
          title: '知识产权保护与商标注册'
        },
        {
          id: 3,
          imageUrl: '/images/lunbo/003.png',
          type: 'page',
          linkUrl: '/pages/shop/shop?category=partner',
          title: '企业并购与资产重组'
        },
        {
          id: 4,
          imageUrl: '/images/lunbo/004.jpg',
          type: 'page',
          linkUrl: '/pages/shop/shop?category=peripheral',
          title: '公司注册与工商变更'
        }
      ]
    });
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const app = getApp();
    const globalData = app.globalData || {};
    console.log('个人中心页面检查登录状态');
    this.setData({ loading: true });

    // 首先检查全局状态
    if (globalData.isLogin && globalData.userInfo) {
      console.log('全局状态显示已登录，使用全局用户信息');

      // 更新页面数据
      this.setData({
        userInfo: globalData.userInfo,
        isLogin: true,
        loading: false
      });

      // 获取用户统计数据
      this.getUserStats();

      // 仍然验证登录状态，但不阻塞UI显示
      this.validateLoginStateInBackground();
      return;
    }

    // 尝试从本地存储获取登录状态
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');

    // 如果本地有登录状态，先使用本地状态快速显示UI
    if (loginState && loginState.isLogin && userInfo) {
      console.log('从本地存储找到登录状态，先使用本地状态');

      // 更新全局状态
      globalData.userInfo = userInfo;
      globalData.isLogin = true;

      // 更新页面状态
      this.setData({
        userInfo: userInfo,
        isLogin: true,
        loading: false
      });

      // 获取用户统计数据
      this.getUserStats();

      // 在后台验证登录状态
      this.validateLoginStateInBackground();
      return;
    }

    // 使用登录状态管理器验证登录状态
    loginStateManager.validateLoginState()
      .then(result => {
        console.log('个人中心页面登录状态验证结果:', result);

        if (result.isValid) {
          // 登录状态有效
          console.log('登录状态有效，用户信息:', result.userInfo);

          // 更新全局用户信息
          globalData.userInfo = result.userInfo;
          globalData.isLogin = true;          // 更新页面数据
          this.setData({
            userInfo: result.userInfo,
            isLogin: true,
            loading: false
          });

          // 获取用户统计数据
          this.getUserStats();
          
          // 获取用户VIP信息
          this.getUserVipInfo();
        } else {
          // 登录状态无效，但不立即清除，而是检查是否有本地状态可用
          console.warn('登录状态验证结果无效:', result.message);

          // 如果是由于网络问题导致的验证失败，尝试使用本地状态
          if (loginState && userInfo) {
            console.log('尝试使用本地登录状态');

            // 更新全局状态
            globalData.userInfo = userInfo;
            globalData.isLogin = true;

            // 更新页面状态
            this.setData({
              userInfo: userInfo,
              isLogin: true,
              loading: false
            });

            return;
          }

          // 如果没有可用的本地状态，才清除登录状态
          console.warn('没有可用的本地状态，清除登录状态');
          loginStateManager.clearLoginState();

          // 更新全局状态
          globalData.userInfo = null;
          globalData.isLogin = false;

          // 更新页面状态
          this.setData({
            userInfo: null,
            isLogin: false,
            stats: this.data.defaultStats,
            loading: false
          });

          // 如果是由于ID不一致导致的无效，显示提示
          if (result.message === '用户ID不一致') {
            wx.showModal({
              title: '登录状态异常',
              content: '检测到登录状态异常，将重新登录以确保数据一致性',
              showCancel: false,
              success: () => {
                // 跳转到登录页
                wx.navigateTo({
                  url: '/pages/auth/auth'
                });
              }
            });
          }
        }
      })
      .catch(err => {
        console.error('验证登录状态出错:', err);

        // 网络错误时，尝试使用本地状态
        const loginState = loginStateManager.getLoginState();
        const userInfo = wx.getStorageSync('userInfo');

        if (loginState && userInfo) {
          console.log('网络错误，使用本地登录状态');

          // 更新全局状态
          globalData.userInfo = userInfo;
          globalData.isLogin = true;

          // 更新页面状态
          this.setData({
            userInfo: userInfo,
            isLogin: true,
            loading: false
          });

          // 获取用户统计数据
          this.getUserStats();
        } else {
          // 如果没有可用的本地状态，才清除登录状态
          loginStateManager.clearLoginState();

          // 更新全局状态
          globalData.userInfo = null;
          globalData.isLogin = false;

          // 更新页面状态
          this.setData({
            userInfo: null,
            isLogin: false,
            stats: this.data.defaultStats,
            loading: false
          });
        }
      });
  },

  // 在后台验证登录状态
  validateLoginStateInBackground: function() {
    loginStateManager.validateLoginState()
      .then(result => {
        console.log('后台验证登录状态结果:', result);
        const app = getApp();
        const globalData = app.globalData || {};

        // 即使后台验证失败，也不立即清除登录状态
        // 这样可以避免用户体验中断
        if (!result.isValid && !result.usingLocalState) {
          console.warn('后台验证发现登录状态无效，但不立即清除:', result.message);

          // 不立即清除登录状态和更新UI，而是在下次应用启动时处理
          // 这样可以避免用户当前会话中突然登出

          // 记录验证失败，但不影响当前会话
          globalData.loginValidationFailed = true;
        } else if (result.userInfo) {
          // 如果有用户信息，确保全局状态和页面状态保持一致
          globalData.userInfo = result.userInfo;
          globalData.isLogin = true;

          // 更新页面状态，确保显示正确的用户信息
          if (!this.data.isLogin || !this.data.userInfo) {
            this.setData({
              userInfo: result.userInfo,
              isLogin: true
            });

            // 获取用户统计数据
            this.getUserStats();
          }
        }
      })
      .catch(err => {
        console.error('后台验证登录状态出错:', err);
        // 网络错误时不做任何处理，保留本地登录状态
      });
  },

  // 使用本地存储的用户信息 - 已不再使用，由loginStateManager替代
  useLocalUserInfo: function() {
    const app = getApp();

    // 使用登录状态管理器验证登录状态
    loginStateManager.validateLoginState()
      .then(result => {
        if (result.isValid) {
          app.globalData.userInfo = result.userInfo;
          app.globalData.isLogin = true;
          this.setData({
            userInfo: result.userInfo,
            isLogin: true,
            loading: false
          });

          this.getUserStats();
        } else {
          // 未登录状态，使用默认数据
          this.setData({
            isLogin: false,
            stats: this.data.defaultStats, // 使用默认数据（全部为0）
            loading: false
          });
        }
      })
      .catch(() => {
        // 未登录状态，使用默认数据
        this.setData({
          isLogin: false,
          stats: this.data.defaultStats, // 使用默认数据（全部为0）
          loading: false
        });
      });
  },

  // 获取用户统计数据（真实数据）
  getUserStats: function() {
    const app = getApp();
    const userInfo = app.globalData.userInfo;
    if (!userInfo || !userInfo.id) {
      this.setData({ stats: this.data.defaultStats });
      return;
    }
    userApi.getUserInfo(userInfo.id).then(res => {
      if (res.success && res.data) {
        // 兼容后端字段
        const d = res.data;
        this.setData({
          stats: {
            followCount: d.followCount || 0,
            followerCount: d.fansCount || d.followerCount || 0,
            likeCount: d.likeCount || 0,
            favoriteCount: d.favoriteCount || 0,
            points: d.points || 0,
            balance: d.balance || 0,
            postCount: d.postCount || 0,
            commentCount: d.commentCount || 0,
            promotionCount: d.promotionCount || 0,
            unpaidCount: d.unpaidCount || 0,
            unshippedCount: d.unshippedCount || 0,
            unreceivedCount: d.unreceivedCount || 0,
            unratedCount: d.unratedCount || 0
          }
        });
      } else {
        this.setData({ stats: this.data.defaultStats });
      }
    }).catch(() => {
      this.setData({ stats: this.data.defaultStats });
    });
  },

  // 获取用户VIP信息
  getUserVipInfo: function() {
    if (!this.data.isLogin) return;
    
    vipApi.getUserVipInfo()
      .then(res => {
        if (res.success && res.data) {
          console.log('获取用户VIP信息成功:', res.data);
          this.setData({
            vipInfo: res.data
          });
        } else {
          console.log('获取用户VIP信息：无数据', res);
          // 如果没有VIP信息，设置为默认
          this.setData({
            vipInfo: {
              level_code: 'free',
              level_name: '普通用户',
              expire_time: null
            }
          });
        }
      })
      .catch(err => {
        console.error('获取用户VIP信息失败:', err);
      });
  },

  // 跳转到登录页
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/auth/auth'
    });
  },

  // 编辑资料
  editProfile: function() {
    wx.navigateTo({
      url: '/pages/profile/edit'
    });
  },

  // 查看关注
  viewFollowing: function() {
    const app = getApp();
    console.log('[viewFollowing] 点击关注，当前isLogin:', app.globalData.isLogin, 'userInfo:', app.globalData.userInfo);
    
    // 使用全局登录检查方法，与粉丝点击使用相同的逻辑
    const result = app.checkNeedLogin((isLogin) => {
      console.log('[viewFollowing] checkNeedLogin回调 isLogin:', isLogin);
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/user/my-follow',
          success: () => { console.log('[viewFollowing] 跳转成功'); },
          fail: (err) => { console.error('[viewFollowing] 跳转失败', err); }
        });
      } else {
        console.log('[viewFollowing] 未登录，未跳转');
      }
    });

    if (result) {
      wx.navigateTo({
        url: '/pages/user/my-follow',
        success: () => { console.log('[viewFollowing] 直接跳转成功'); },
        fail: (err) => { console.error('[viewFollowing] 直接跳转失败', err); }
      });
    }
  },

  // 查看粉丝
  viewFollowers: function() {
    const app = getApp();
    console.log('[viewFollowers] 点击粉丝，当前isLogin:', app.globalData.isLogin, 'userInfo:', app.globalData.userInfo);
    // 使用全局登录检查方法
    const result = app.checkNeedLogin((isLogin) => {
      console.log('[viewFollowers] checkNeedLogin回调 isLogin:', isLogin);
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/profile/followers',
          success: () => { console.log('[viewFollowers] 跳转成功'); },
          fail: (err) => { console.error('[viewFollowers] 跳转失败', err); }
        });
      } else {
        console.log('[viewFollowers] 未登录，未跳转');
      }
    });
    console.log('[viewFollowers] checkNeedLogin返回值:', result);
    if (result) {
      wx.navigateTo({
        url: '/pages/profile/followers',
        success: () => { console.log('[viewFollowers] 直接跳转成功'); },
        fail: (err) => { console.error('[viewFollowers] 直接跳转失败', err); }
      });
    }
  },

  // 查看获赞
  viewLikes: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/profile/likes'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/profile/likes'
    });
  },

  // 查看收藏
  viewFavorites: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/profile/favorites'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/profile/favorites'
    });
  },

  // 查看积分
  viewPoints: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/points/points'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/points/points'
    });
  },

  // 查看余额
  viewBalance: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/balance/balance'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/balance/balance'
    });
  },

  // 查看我的推广
  viewMyPromotions: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/profile/promotions'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/profile/promotions'
    });
  },

  // 我的帖子
  viewMyPosts: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/profile/posts'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/profile/posts'
    });
  },

  // 我的评论
  viewMyComments: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/profile/my-comments'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/profile/my-comments'
    });
  },

  // 我的收藏
  viewMyFavorites: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/profile/favorites'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/profile/favorites'
    });
  },

  // 全部订单
  viewAllOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=all'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=all'
    });
  },

  // 待付款
  viewUnpaidOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=unpaid'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=unpaid'
    });
  },

  // 待发货
  viewUnshippedOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=unshipped'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=unshipped'
    });
  },

  // 待收货
  viewUnreceivedOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=unreceived'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=unreceived'
    });
  },

  // 退换货
  viewUnratedOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=unrated'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=unrated'
    });
  },

  // 收货地址
  viewAddress: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/address/list'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/address/list'
    });
  },

  // 客服中心
  contactService: function() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  },

  // 设置
  goToSettings: function() {
    const app = getApp();
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '请先登录',
        content: '登录后可进入账号设置',
        showCancel: true,
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        }
      });
      return;
    }
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  // 关于我们
  goToAbout: function() {
    wx.navigateTo({
      url: '/pages/about/index'
    });
  },

  // 轮播图点击事件
  onBannerTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const url = e.currentTarget.dataset.url;

    // 找到对应的轮播图数据
    const banner = this.data.banners.find(item => item.id === id);

    if (banner && banner.linkUrl) {
      if (banner.type === 'url') {
        // 打开网页
        wx.navigateTo({
          url: `/pages/webview/webview?url=${encodeURIComponent(banner.linkUrl)}`
        });
      } else if (banner.type === 'page') {
        // 打开小程序页面
        wx.navigateTo({
          url: banner.linkUrl,
          fail: function(err) {
            console.error('轮播图跳转失败', err);
            // 如果navigateTo失败，尝试使用switchTab
            wx.switchTab({
              url: banner.linkUrl.split('?')[0],
              fail: function(switchErr) {
                console.error('switchTab也失败了', switchErr);
                wx.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                });
              }
            });
          }
        });
      }
    }
  },

  // 退出登录
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 使用登录状态管理器登出
          loginStateManager.logout()
            .then(() => {
              // 清除全局数据
              const app = getApp();
              app.globalData.userInfo = null;
              app.globalData.isLogin = false;

              // 更新页面状态
              this.setData({
                userInfo: null,
                isLogin: false,
                stats: this.data.defaultStats // 使用默认数据（全部为0）
              });

              // 确保轮播图数据仍然存在
              if (this.data.banners.length === 0) {
                console.log('退出登录后轮播图数据为空，重新获取');
                this.getBanners();
              }

              wx.showToast({
                title: '已退出登录',
                icon: 'success'
              });
            });
        }
      }
    });
  },

  // 开发者选项已移除

  // 跳转到我的发布
  viewMyPublish: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/user/publish'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/user/publish'
    });
  },

  // 跳转到帖子评论页
  goToPostComments: function(e) {
    const postId = e.currentTarget.dataset.postid;
    if (!postId) return;
    wx.navigateTo({
      url: `/pages/post/comments?postId=${postId}`
    });
  }
});
