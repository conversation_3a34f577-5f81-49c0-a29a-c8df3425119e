/**
 * 商品模型
 */
const db = require('../config/db');

class Product {
  static async findAll(options = {}) {
    const { categoryId, subCategoryId, keyword, page = 1, pageSize = 10, sortType = 'default' } = options;

    let sql = 'SELECT * FROM products WHERE 1=1';
    const params = [];

    if (typeof categoryId !== 'undefined' && categoryId !== null && String(categoryId).trim() !== '') {
      sql += ' AND categoryId = ?';
      params.push(Number(categoryId));
    }

    if (typeof subCategoryId !== 'undefined' && subCategoryId !== null && String(subCategoryId).trim() !== '') {
      sql += ' AND subCategoryId = ?';
      params.push(Number(subCategoryId));
    }

    if (keyword) {
      sql += ' AND (name LIKE ? OR description LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 排序，兼容所有前端写法
    if (sortType === 'price_asc' || sortType === 'price-asc') {
      sql += ' ORDER BY price ASC';
    } else if (sortType === 'price_desc' || sortType === 'price-desc') {
      sql += ' ORDER BY price DESC';
    } else if (sortType === 'sales') {
      sql += ' ORDER BY salesCount DESC';
    } else {
      sql += ' ORDER BY id DESC';
    }

    // 分页
    const safePage = parseInt(page, 10) > 0 ? parseInt(page, 10) : 1;
    const safePageSize = parseInt(pageSize, 10) > 0 ? parseInt(pageSize, 10) : 10;
    const offset = (safePage - 1) * safePageSize;
    sql += ` LIMIT ${Number(safePageSize)} OFFSET ${Number(offset)}`;
    console.log('Product SQL params:', params);

    return await db.query(sql, params);
  }

  static async findById(id) {
    console.log('=====================================');
    console.log('查询商品详情, 原始id:', id, '原始类型:', typeof id);

    // ID类型转换
    let queryId = id;
    if (typeof id === 'string' && !isNaN(id)) {
      queryId = parseInt(id);
    }
    console.log('转换后的queryId:', queryId, '转换后类型:', typeof queryId);

    const sql = 'SELECT * FROM products WHERE id = ?';
    console.log('SQL:', sql, 'params:', [queryId]);
    const result = await db.query(sql, [queryId]);
    console.log('查询结果:', JSON.stringify(result, null, 2));
    console.log('=====================================');
    return result.length > 0 ? result[0] : null;
  }

  static async getCategories() {
    return await db.query('SELECT * FROM categories ORDER BY id');
  }

  static async getShopCategories() {
    return await db.query('SELECT * FROM categories ORDER BY id');
  }

  static async getShopSubCategories(parentId) {
    try {
      const sql = 'SELECT * FROM subCategories WHERE parentId = ?';
      const params = [parentId];
      return await db.query(sql, params);
    } catch (error) {
      console.error('执行SQL查询失败:', error);
      throw error;
    }
  }

  static async getBanners() {
    // 修复：使用id排序而不是sort字段，因为数据库中banners表没有sort字段
    try {
      const banners = await db.query('SELECT * FROM banners ORDER BY id');
      return banners.map(banner => ({
        id: banner.id,
        imageUrl: banner.imageUrl,
        linkUrl: banner.linkUrl,
        title: banner.title,
        type: banner.type || 'page'
      }));
    } catch (error) {
      console.error('查询banners表失败:', error.message);
      // 如果banners表不存在或查询失败，返回空数组
      return [];
    }
  }

  static async count(options = {}) {
    const { categoryId, subCategoryId, keyword } = options;

    let sql = 'SELECT COUNT(*) as total FROM products WHERE 1=1';
    const params = [];

    if (categoryId !== undefined && categoryId !== null && categoryId !== '') {
      sql += ' AND categoryId = ?';
      params.push(Number(categoryId));
    }

    if (subCategoryId !== undefined && subCategoryId !== null && subCategoryId !== '') {
      sql += ' AND subCategoryId = ?';
      params.push(Number(subCategoryId));
    }

    if (keyword) {
      sql += ' AND (name LIKE ? OR description LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    const result = await db.query(sql, params);
    return result[0].total;
  }
}

module.exports = Product;
