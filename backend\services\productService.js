/**
 * 商品服务
 */
const Product = require('../models/Product');

class ProductService {
  async getProducts(options) {
    try {
      const products = await Product.findAll(options);
      const total = await Product.count(options);
      
      return {
        success: true,
        data: {
          list: products,
          total,
          page: parseInt(options.page) || 1,
          pageSize: parseInt(options.pageSize) || 10
        },
        message: '获取商品列表成功'
      };
    } catch (error) {
      console.error('获取商品列表失败:', error);
      throw error;
    }
  }
  
  async getProductById(id) {
    try {
      const product = await Product.findById(id);
      if (!product) {
        return {
          success: false,
          message: '商品不存在'
        };
      }
      
      return {
        success: true,
        data: product,
        message: '获取商品详情成功'
      };
    } catch (error) {
      console.error('获取商品详情失败:', error);
      throw error;
    }
  }
  
  async getCategories() {
    try {
      const categories = await Product.getCategories();
      
      return {
        success: true,
        data: categories,
        message: '获取分类列表成功'
      };
    } catch (error) {
      console.error('获取分类列表失败:', error);
      throw error;
    }
  }
  
  async getShopCategories() {
    try {
      const categories = await Product.getShopCategories();
      
      return {
        success: true,
        data: categories,
        message: '获取商城分类列表成功'
      };
    } catch (error) {
      console.error('获取商城分类列表失败:', error);
      throw error;
    }
  }
  
  async getShopSubCategories(parentId) {
    try {
      const subCategories = await Product.getShopSubCategories(parentId);
      
      return {
        success: true,
        data: subCategories,
        message: '获取商城子分类列表成功'
      };
    } catch (error) {
      console.error('获取商城子分类列表失败:', error);
      throw error;
    }
  }
  
  async getBanners() {
    try {
      const banners = await Product.getBanners();
      
      return {
        success: true,
        data: banners,
        message: '获取轮播图列表成功'
      };
    } catch (error) {
      console.error('获取轮播图列表失败:', error);
      throw error;
    }
  }
}

module.exports = new ProductService();
