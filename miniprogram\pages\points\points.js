// pages/points/points.js
const { productApi, pointsApi, userApi } = require('../../utils/api');
const { validateLoginState } = require('../../utils/login-state-manager');

Page({  /**
   * 页面的初始数据
   */  data: {
    userInfo: {},
    banners: [],
    bannerTitles: [],
    activeTab: 'consume', // consume/gain
    consumeRule: '',
    gainWays: '',
    loading: true,
    pointsRecords: [],
    loadingRecords: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.checkLoginAndInit();    // 直接设置本地轮播图
    this.setData({
      banners: [
        '/images/lunbo/001.jpeg',
        '/images/lunbo/002.jpg',
        '/images/lunbo/003.png',
        '/images/lunbo/004.jpg',
        '/images/lunbo/004.webp'
      ],
      bannerTitles: [
        '每日签到赚积分',
        '分享好友获积分',
        '积分兑换精美礼品',
        '使用积分查看联系方式',
        '积分可抵扣购物'
      ]
    });
    setTimeout(() => {
      console.log('页面userInfo:', this.data.userInfo);
      console.log('页面banners:', this.data.banners);
    }, 1000);
  },
  // 检查登录并初始化
  checkLoginAndInit() {
    const that = this;
    validateLoginState().then(res => {
      if (!res.isValid) {
        wx.showModal({
          title: '请先登录',
          content: '登录后可查看积分中心',
          showCancel: false,
          success: () => {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        });
        return;
      }
      // 已登录，直接用本地全局信息，兼容字段
      const app = getApp();
      let userInfo = (app.globalData && app.globalData.userInfo) || {};

      // 统一字段名，确保显示正确
      userInfo.avatarUrl = userInfo.avatarUrl || userInfo.avatar || '/images/icons2/男头像.png';
      userInfo.avatar = userInfo.avatar || userInfo.avatarUrl || '/images/icons2/男头像.png';
      userInfo.nickName = userInfo.nickName || userInfo.nickname || userInfo.username || '用户昵称';
      userInfo.nickname = userInfo.nickname || userInfo.nickName || userInfo.username || '用户昵称';
      userInfo.id = userInfo.id || userInfo._id || 'XXXXXXXXXX';
      userInfo.points = userInfo.points || 0;

      console.log('积分中心用户信息:', userInfo);
      that.setData({ userInfo });

      // 加载积分配置
      that.loadPointsConfig();

      // 如果有用户ID，加载用户最新积分信息和积分记录
      if (userInfo.id && userInfo.id !== 'XXXXXXXXXX') {
        that.loadUserInfoAndStats(userInfo.id);
      }
    });
  },
  // 获取最新用户信息和积分
  loadUserInfoAndStats(userId) {
    const that = this;
    Promise.all([
      userApi.getUserInfo(userId),
      pointsApi.getUserPoints(userId)
    ]).then(([infoRes, pointsRes]) => {
      let userInfo = (infoRes.success && infoRes.data) ? infoRes.data : {};

      // 统一字段名，确保显示正确
      userInfo.avatarUrl = userInfo.avatarUrl || userInfo.avatar || '/images/icons2/男头像.png';
      userInfo.avatar = userInfo.avatar || userInfo.avatarUrl || '/images/icons2/男头像.png';
      userInfo.nickName = userInfo.nickName || userInfo.nickname || userInfo.username || '用户昵称';
      userInfo.nickname = userInfo.nickname || userInfo.nickName || userInfo.username || '用户昵称';
      userInfo.id = userInfo.id || userInfo._id || userId;

      if (pointsRes.success && pointsRes.data) {
        userInfo.points = pointsRes.data.points || 0;
      } else {
        userInfo.points = userInfo.points || 0;
      }

      console.log('从服务器获取的用户信息:', userInfo);
      that.setData({ userInfo });
      that.loadPointsRecords();
    }).catch((err) => {
      console.error('获取用户信息或积分失败:', err);
      // 失败时回退本地数据
      const app = getApp();
      let fallbackUserInfo = (app.globalData && app.globalData.userInfo) || {};
      // 确保回退数据也有正确的字段
      fallbackUserInfo.avatarUrl = fallbackUserInfo.avatarUrl || fallbackUserInfo.avatar || '/images/icons2/男头像.png';
      fallbackUserInfo.nickName = fallbackUserInfo.nickName || fallbackUserInfo.nickname || '用户昵称';
      fallbackUserInfo.points = fallbackUserInfo.points || 0;
      that.setData({ userInfo: fallbackUserInfo });
    });
  },

  // 加载积分记录
  loadPointsRecords() {
    this.setData({ loadingRecords: true });
    pointsApi.getUserPointsRecords({ limit: 20, offset: 0 })
      .then(res => {
        if (res.success && Array.isArray(res.data)) {
          // 格式化时间
          const records = res.data.map(record => {
            return {
              id: record.id,
              time: this.formatTime(new Date(record.created_at)),
              event: record.event,
              change: record.change_amount,
              balance: record.balance
            };
          });
          this.setData({ pointsRecords: records });
        }
      })
      .catch(err => {
        console.error('获取积分记录失败:', err);
      })
      .finally(() => {
        this.setData({ loadingRecords: false });
      });
  },

  // 格式化时间
  formatTime(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();

    return [year, month, day].map(this.formatNumber).join('年') + '月' +
           day + '日' + ' ' + [hour, minute, second].map(this.formatNumber).join(':');
  },

  // 格式化数字
  formatNumber(n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  },
  // 获取积分规则和获取方式
  loadPointsConfig() {
    pointsApi.getPointsConfig().then(res => {
      if (res.success && res.data) {
        this.setData({
          consumeRule: res.data.consume_rule || '积分可用于兑换商品、参与活动等，具体以平台公告为准。',
          gainWays: res.data.gain_ways || '每日签到、发布内容、参与活动、完成任务等可获得积分。',
          loading: false
        });
      } else {
        // 接口无数据时，显示默认内容
        this.setData({
          consumeRule: '积分可用于兑换商品、参与活动等，具体以平台公告为准。',
          gainWays: '每日签到、发布内容、参与活动、完成任务等可获得积分。',
          loading: false
        });
      }
    }).catch((err) => {
      console.error('获取积分配置失败:', err);
      // 接口404等异常时，显示默认内容
      this.setData({
        consumeRule: '积分可用于兑换商品、参与活动等，具体以平台公告为准。',
        gainWays: '每日签到、发布内容、参与活动、完成任务等可获得积分。',
        loading: false
      });
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次页面显示时同步用户信息
    const app = getApp();
    if (app.globalData.isLogin && app.globalData.userInfo) {
      let userInfo = app.globalData.userInfo;
      // 统一字段名
      userInfo.avatarUrl = userInfo.avatarUrl || userInfo.avatar || '/images/icons2/男头像.png';
      userInfo.nickName = userInfo.nickName || userInfo.nickname || userInfo.username || '用户昵称';
      userInfo.points = userInfo.points || 0;

      console.log('onShow - 更新用户信息:', userInfo);
      this.setData({ userInfo });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.checkLoginAndInit();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
  },

  // 切换卡片
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  }
})
