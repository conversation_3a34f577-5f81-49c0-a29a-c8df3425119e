-- 创建积分相关表的SQL脚本
-- 用于确保积分系统的数据库表存在

-- 1. 创建用户积分表
CREATE TABLE IF NOT EXISTS user_points (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  points INT NOT NULL DEFAULT 0 COMMENT '当前积分',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY unique_user_id (user_id),
  INDEX idx_user_id (user_id),
  INDEX idx_points (points)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分表';

-- 2. 创建积分记录表
CREATE TABLE IF NOT EXISTS points_records (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  change_amount INT NOT NULL COMMENT '积分变动量（正数增加，负数减少）',
  event VARCHAR(255) NOT NULL COMMENT '积分变动事件描述',
  balance INT NOT NULL COMMENT '变动后的积分余额',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX idx_user_id (user_id),
  INDEX idx_created_at (created_at),
  INDEX idx_event (event)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- 3. 创建积分配置表
CREATE TABLE IF NOT EXISTS points_config (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  consume_rule TEXT COMMENT '积分消耗规则',
  gain_ways TEXT COMMENT '积分获取方式',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分配置表';

-- 4. 插入默认积分配置（如果不存在）
INSERT IGNORE INTO points_config (id, consume_rule, gain_ways) VALUES (
  1,
  '积分可用于兑换商品、参与活动、查看联系方式等，具体以平台公告为准。使用积分时将按照先进先出的原则进行扣除。',
  '每日签到、发布内容、参与活动、完成任务、邀请好友等可获得积分。具体积分获取规则请关注平台活动公告。'
);

-- 5. 显示创建结果
SELECT 'Points tables created successfully!' as result;
